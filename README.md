# Auspex Records

A complete web platform for Auspex Records, featuring a modern React frontend and AWS serverless backend with separate staging and production environments.

## 🌐 Live Sites

| Environment | URL | Status |
|-------------|-----|--------|
| **Production** | [auspexrecords.com](https://auspexrecords.com) | ✅ Live |
| **Staging** | [stage.auspexrecords.com](https://stage.auspexrecords.com) | ✅ Live |

## 📁 Project Structure

```
auspex/
├── auspex-website/         # Main website application
│   ├── src/               # React frontend source code
│   │   ├── components/    # React components (including ShadCN UI)
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom React hooks
│   │   └── lib/           # Utility functions
│   ├── public/            # Static assets
│   │   └── assets/        # Logos, release covers, etc.
│   ├── terraform/         # Infrastructure as Code
│   ├── lambda/            # Serverless API functions
│   └── dist/              # Built frontend assets
└── tools/                 # Development and deployment tools
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- AWS CLI configured
- Terraform 1.2+

### Development Setup
```bash
# Clone and setup
cd auspex-website
npm install

# Start development server
npm run dev
```

### Deploy to Staging
```bash
npm run deploy:staging
```

### Deploy to Production
```bash
npm run deploy:prod
```

## 🏗️ Architecture

### Frontend
- **React 18** with TypeScript
- **ShadCN UI** for component library
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **Vite** for build tooling
- **React Router** for navigation

### Backend (AWS Serverless)
- **S3** for static website hosting
- **CloudFront** for global CDN
- **API Gateway** for REST API
- **Lambda** for serverless functions
- **DynamoDB** for data storage
- **Route53** for DNS management
- **ACM** for SSL certificates

### Infrastructure
- **Terraform** for Infrastructure as Code
- **Multi-environment** setup (staging/production)
- **Modular architecture** with reusable components
- **Automated state management** with S3 backend

## 📋 Available Commands

### Development
```bash
npm run dev          # Start development server
npm run build        # Build for current environment
npm run lint         # Run ESLint
npm run preview      # Preview built application
```

### Environment-Specific Builds
```bash
npm run build:staging    # Build with staging API
npm run build:prod       # Build with production API
```

### Deployment
```bash
npm run deploy:staging   # Deploy to staging environment
npm run deploy:prod      # Deploy to production environment
```

## 🔧 Configuration

### Environment Variables
The project uses environment-specific API URLs:
- **Staging**: `https://ma1l0omu6k.execute-api.us-west-1.amazonaws.com`
- **Production**: `https://e4n0a6vbud.execute-api.us-west-1.amazonaws.com`

### Development Environment
Create `.env` file in `auspex-website/`:
```bash
VITE_API_URL=https://ma1l0omu6k.execute-api.us-west-1.amazonaws.com
```

## 📚 Documentation

- [Website Documentation](./auspex-website/README.md) - Detailed frontend and deployment guide
- [Terraform Documentation](./auspex-website/terraform/README.md) - Infrastructure setup and management

## 🎵 Features

### **Core Functionality**
- **Music Releases** - Browse and stream releases with embedded YouTube videos
- **Live Performances** - Watch recorded live performances
- **Multi-Platform Links** - Direct links to Spotify, Bandcamp, SoundCloud, etc.
- **Download Options** - Multiple audio formats (FLAC, MP3, WAV, etc.)

### **User Experience**
- **Interactive Animations** - Dynamic background with floating album covers
- **Particle System** - Mouse-interactive particle effects
- **Scroll Animations** - Smooth reveal animations on scroll
- **Glass Morphism UI** - Modern glass-effect design elements
- **Responsive Design** - Optimized for all devices
- **Dark Theme** - Music-focused dark interface with orange accents

### **Performance**
- **Fast Loading** - Global CDN with optimized assets
- **Progressive Enhancement** - Works without JavaScript
- **Optimized Images** - Lazy loading and proper formats
- **No Duplicate Content** - Smart rotation system for animations

## 🔄 Deployment Workflow

1. **Make changes** to the codebase
2. **Test locally** with `npm run dev`
3. **Deploy to staging** with `npm run deploy:staging`
4. **Test staging** at https://stage.auspexrecords.com
5. **Deploy to production** with `npm run deploy:prod`
6. **Verify production** at https://auspexrecords.com

## 🛠️ Infrastructure Management

### Initial Setup
```bash
# Bootstrap Terraform state management
cd auspex-website/terraform/bootstrap
terraform init && terraform apply

# Deploy staging environment
cd ../environments/staging
terraform init && terraform apply

# Deploy production environment  
cd ../prod
terraform init && terraform apply
```

### Ongoing Management
- Use `npm run deploy:staging` and `npm run deploy:prod` for frontend updates
- Use Terraform directly for infrastructure changes
- State files are automatically managed in S3 with DynamoDB locking

## 📊 Current Releases

The platform showcases releases from various artists including:
- **Aeromancer** - Ion Tentacles
- **Caixedia Camista** - Midnight Sanctuary
- **Haavi** - Wisdom of the World Vol. 1
- **Hunting Hush** - Moksha Island
- **Maru Secrets** - Time Crystal
- **Oak Project** - Reflections
- **Paranoiac** - Pots
- **Samyaza** - II
- **Zaar** - Psykopomps

All releases feature dynamic background animations with no duplicate covers displayed simultaneously.

## 🤝 Contributing

### **Development Workflow**
1. Make changes in the `auspex-website` directory
2. Test locally with `npm run dev`
3. Deploy to staging for testing
4. Deploy to production when ready

### **Best Practices**
- **Components**: Use ShadCN UI components when possible
- **Animations**: Only animate interactive elements on hover
- **Performance**: Implement proper cleanup for useEffect hooks
- **Assets**: All assets should be in `auspex-website/public/assets/`
- **Dependencies**: Use package managers, don't edit package files manually
- **Testing**: Always test on staging before production deployment

### **Code Standards**
- TypeScript for type safety
- Tailwind CSS for styling
- Framer Motion for animations
- Proper component separation and reusability

## 📄 License

Private project for Auspex Records.
