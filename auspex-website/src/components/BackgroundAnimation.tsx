import { useEffect, useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const releaseCovers = [
  '/assets/release-covers/Aeromancer - Ion Tentacles.jpg',
  '/assets/release-covers/Caixedia Camista - Midnight Sanctuary.jpg',
  '/assets/release-covers/Haavi - Wisdom of the World Vol. 1.jpg',
  '/assets/release-covers/Hunting Hush - Moksha Island.jpg',
  '/assets/release-covers/Maru Secrets - Time Crystal.jpg',
  '/assets/release-covers/Oak Project - Reflections.jpeg',
  '/assets/release-covers/Paranoiac - Pots.jpg',
  '/assets/release-covers/Samyaza - II.jpg',
  '/assets/release-covers/Zaar - Psykopomps.jpg',
];

interface FloatingCover {
  id: string;
  src: string;
  x: number;
  y: number;
  rotation: number;
  scale: number;
  duration: number;
  appearTime: number;
  lifespan: number;
}

interface BackgroundAnimationProps {
  className?: string;
}

export const BackgroundAnimation = ({ className = "" }: BackgroundAnimationProps) => {
  const [activeCover, setActiveCovers] = useState<FloatingCover[]>([]);
  const [isStarted, setIsStarted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const coverIdCounter = useRef(0);
  const usedCovers = useRef<Set<string>>(new Set());
  const availableCovers = useRef<string[]>([...releaseCovers]);

  // Generate random position that doesn't overlap too much
  const generatePosition = useCallback(() => {
    return {
      x: Math.random() * 70 + 15, // 15% to 85% to avoid edges
      y: Math.random() * 70 + 15, // 15% to 85% to avoid edges
    };
  }, []);

  // Get next unique cover (no duplicates until all are used)
  const getNextCoverSrc = useCallback(() => {
    // If we've used all covers, reset the cycle
    if (availableCovers.current.length === 0) {
      availableCovers.current = [...releaseCovers];
      usedCovers.current.clear();
    }

    // Get a random cover from available ones
    const randomIndex = Math.floor(Math.random() * availableCovers.current.length);
    const selectedCover = availableCovers.current[randomIndex];

    // Remove from available and add to used
    availableCovers.current.splice(randomIndex, 1);
    usedCovers.current.add(selectedCover);

    return selectedCover;
  }, []);

  // Create a new cover with random properties
  const createNewCover = useCallback(() => {
    const coverSrc = getNextCoverSrc();
    const position = generatePosition();

    return {
      id: `cover-${coverIdCounter.current++}`,
      src: coverSrc,
      x: position.x,
      y: position.y,
      rotation: Math.random() * 360,
      scale: 0.6 + Math.random() * 0.3, // 0.6 to 0.9 scale
      duration: 12 + Math.random() * 8, // 12-20 seconds for floating animation
      appearTime: Date.now(),
      lifespan: 6000 + Math.random() * 8000, // 6-14 seconds before disappearing
    };
  }, [generatePosition, getNextCoverSrc]);

  // Check if a cover source is already active
  const isCoverActive = useCallback((src: string, activeCovers: FloatingCover[]) => {
    return activeCovers.some(cover => cover.src === src);
  }, []);

  // Add a new cover to the animation (ensuring no duplicates)
  const addCover = useCallback(() => {
    setActiveCovers(prev => {
      // Limit to maximum 6 covers at once (since we have 9 unique covers)
      if (prev.length >= 6) {
        return prev;
      }

      // Try to create a new cover that's not already active
      let attempts = 0;
      let newCover;

      do {
        newCover = createNewCover();
        attempts++;
      } while (isCoverActive(newCover.src, prev) && attempts < 15);

      // If we couldn't find a unique cover after 15 attempts, don't add
      if (isCoverActive(newCover.src, prev)) {
        return prev;
      }

      return [...prev, newCover];
    });
  }, [createNewCover, isCoverActive]);

  // Remove expired covers
  const removeExpiredCovers = useCallback(() => {
    const now = Date.now();
    setActiveCovers(prev =>
      prev.filter(cover => (now - cover.appearTime) < cover.lifespan)
    );
  }, []);

  // Start the animation sequence immediately
  useEffect(() => {
    if (!isStarted) {
      setIsStarted(true);

      // Add multiple covers immediately for instant visual impact
      for (let i = 0; i < 5; i++) {
        setTimeout(() => addCover(), i * 150); // Stagger by 150ms each
      }

      // Start continuous cycling immediately
      intervalRef.current = setInterval(() => {
        setActiveCovers(prev => {
          const now = Date.now();
          // Remove expired covers
          const activeCover = prev.filter(cover => (now - cover.appearTime) < cover.lifespan);

          // Try to maintain 4-5 covers (since we have 9 unique covers total)
          const newCovers = [...activeCover];

          // Add new covers if we have less than 4, ensuring no duplicates
          while (newCovers.length < 4) {
            let attempts = 0;
            let newCover;

            do {
              newCover = createNewCover();
              attempts++;
            } while (isCoverActive(newCover.src, newCovers) && attempts < 10);

            if (!isCoverActive(newCover.src, newCovers)) {
              newCovers.push(newCover);
            } else {
              break; // Can't find unique cover, stop trying
            }
          }

          // Randomly add one more cover up to 5 total if possible
          if (newCovers.length < 5 && Math.random() > 0.4) {
            let attempts = 0;
            let newCover;

            do {
              newCover = createNewCover();
              attempts++;
            } while (isCoverActive(newCover.src, newCovers) && attempts < 10);

            if (!isCoverActive(newCover.src, newCovers)) {
              newCovers.push(newCover);
            }
          }

          return newCovers;
        });
      }, 1500); // Check every 1.5 seconds for faster cycling
    }
  }, [isStarted, addCover, createNewCover]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <div className={`absolute inset-0 overflow-hidden ${className}`}>
      <AnimatePresence mode="sync">
        {activeCover.map((cover) => (
          <motion.div
            key={cover.id}
            className="absolute pointer-events-none"
            style={{
              left: `${cover.x}%`,
              top: `${cover.y}%`,
            }}
            initial={{
              opacity: 0,
              scale: 0,
              rotate: cover.rotation - 90,
            }}
            animate={{
              opacity: [0, 0.8, 0.6, 0.8],
              scale: [0, cover.scale * 1.2, cover.scale, cover.scale * 1.1, cover.scale],
              rotate: [cover.rotation - 90, cover.rotation, cover.rotation + 180, cover.rotation + 270],
              x: [0, 20, -15, 10, 0],
              y: [0, -15, 25, -10, 0],
            }}
            exit={{
              opacity: 0,
              scale: 0,
              rotate: cover.rotation + 180,
              transition: {
                duration: 1.0,
                ease: "easeInOut"
              }
            }}
            transition={{
              duration: cover.duration,
              repeat: Infinity,
              ease: "easeInOut",
              times: [0, 0.25, 0.5, 0.75, 1],
            }}
          >
            <div className="relative">
              <img
                src={cover.src}
                alt="Release cover"
                className="w-28 h-28 md:w-40 md:h-40 lg:w-48 lg:h-48 rounded-xl shadow-2xl border border-auspex-orange-500/30 transition-all duration-500"
                loading="lazy"
                draggable={false}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-auspex-navy-900/50 via-transparent to-auspex-orange-500/20 rounded-xl opacity-70" />
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};
