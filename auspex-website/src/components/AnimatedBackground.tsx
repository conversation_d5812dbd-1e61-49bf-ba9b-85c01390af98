import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

interface BackgroundProps {
  variant?: 'floating' | 'particles' | 'waves' | 'minimal';
  intensity?: 'low' | 'medium' | 'high';
  releaseCovers?: string[];
}

interface FloatingElement {
  id: number;
  src?: string;
  x: number;
  y: number;
  size: number;
  rotation: number;
  duration: number;
  delay: number;
  opacity: number;
}

export const AnimatedBackground = ({ 
  variant = 'floating', 
  intensity = 'medium',
  releaseCovers = []
}: BackgroundProps) => {
  const [elements, setElements] = useState<FloatingElement[]>([]);

  const getElementCount = () => {
    const counts = {
      low: { floating: 6, particles: 15, waves: 3 },
      medium: { floating: 12, particles: 25, waves: 5 },
      high: { floating: 20, particles: 40, waves: 8 }
    };
    return counts[intensity][variant as keyof typeof counts.low] || 12;
  };

  useEffect(() => {
    const count = getElementCount();
    const newElements: FloatingElement[] = [];

    for (let i = 0; i < count; i++) {
      newElements.push({
        id: i,
        src: releaseCovers.length > 0 ? releaseCovers[i % releaseCovers.length] : undefined,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: variant === 'particles' ? 2 + Math.random() * 4 : 80 + Math.random() * 120,
        rotation: Math.random() * 360,
        duration: 10 + Math.random() * 20,
        delay: Math.random() * 5,
        opacity: variant === 'particles' ? 0.4 + Math.random() * 0.4 : 0.1 + Math.random() * 0.3,
      });
    }

    setElements(newElements);
  }, [variant, intensity, releaseCovers]);

  const renderFloatingCovers = () => (
    <>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: `${element.size}px`,
            height: `${element.size}px`,
          }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{
            opacity: element.opacity,
            scale: [0.8, 1.2, 0.8],
            x: [0, 50, -30, 0],
            y: [0, -40, 30, 0],
            rotate: [element.rotation, element.rotation + 360],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            delay: element.delay,
            ease: "linear",
          }}
        >
          {element.src ? (
            <img
              src={element.src}
              alt="Release cover"
              className="w-full h-full object-cover rounded-lg shadow-2xl blur-sm hover:blur-none transition-all duration-500"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-auspex-orange-500/20 to-auspex-purple-500/20 rounded-lg backdrop-blur-sm" />
          )}
        </motion.div>
      ))}
    </>
  );

  const renderParticles = () => (
    <>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute rounded-full"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: `${element.size}px`,
            height: `${element.size}px`,
          }}
          initial={{ opacity: 0 }}
          animate={{
            opacity: [0, element.opacity, 0],
            y: [0, -200, -400],
            x: [0, Math.sin(element.id) * 50, Math.cos(element.id) * 30],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            delay: element.delay,
            ease: "easeOut",
          }}
        >
          <div 
            className="w-full h-full rounded-full"
            style={{
              background: `radial-gradient(circle, ${
                element.id % 3 === 0 ? '#FF4F00' : 
                element.id % 3 === 1 ? '#9333EA' : '#22D3EE'
              }40, transparent)`,
            }}
          />
        </motion.div>
      ))}
    </>
  );

  const renderWaves = () => (
    <>
      {elements.map((element) => (
        <motion.div
          key={element.id}
          className="absolute"
          style={{
            left: `${element.x}%`,
            top: `${element.y}%`,
            width: `${element.size * 3}px`,
            height: `${element.size}px`,
          }}
          initial={{ opacity: 0, scaleX: 0 }}
          animate={{
            opacity: [0, element.opacity, 0],
            scaleX: [0, 2, 0],
            scaleY: [1, 0.5, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: element.duration,
            repeat: Infinity,
            delay: element.delay,
            ease: "easeInOut",
          }}
        >
          <div 
            className="w-full h-full rounded-full"
            style={{
              background: `linear-gradient(90deg, transparent, ${
                element.id % 2 === 0 ? '#FF4F0020' : '#9333EA20'
              }, transparent)`,
              filter: 'blur(2px)',
            }}
          />
        </motion.div>
      ))}
    </>
  );

  const renderMinimal = () => (
    <div className="absolute inset-0">
      <motion.div
        className="absolute inset-0 opacity-30"
        animate={{
          background: [
            'radial-gradient(circle at 20% 80%, #FF4F0010 0%, transparent 50%)',
            'radial-gradient(circle at 80% 20%, #9333EA10 0%, transparent 50%)',
            'radial-gradient(circle at 40% 40%, #22D3EE10 0%, transparent 50%)',
            'radial-gradient(circle at 20% 80%, #FF4F0010 0%, transparent 50%)',
          ],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "linear",
        }}
      />
    </div>
  );

  const renderContent = () => {
    switch (variant) {
      case 'floating':
        return renderFloatingCovers();
      case 'particles':
        return renderParticles();
      case 'waves':
        return renderWaves();
      case 'minimal':
        return renderMinimal();
      default:
        return renderFloatingCovers();
    }
  };

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Base gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-auspex-gray-950 to-auspex-gray-900" />
      
      {/* Animated elements */}
      {renderContent()}
      
      {/* Overlay gradients */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/40" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20" />
    </div>
  );
};
