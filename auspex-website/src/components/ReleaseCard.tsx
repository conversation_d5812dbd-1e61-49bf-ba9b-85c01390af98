import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { HiDownload, HiX, HiPlay } from "react-icons/hi";
import { useScrollAnimation, scaleInVariants } from "../hooks/useScrollAnimation";
import type { Release } from "../types";
import { YouTubeEmbed } from "./YouTubeEmbed";

interface ReleaseCardProps {
  release: Release;
}

// Platform icons with text
const SoundCloudIcon = () => (
  <div className="flex items-center">
    <svg width="20" height="20" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true"><path d="M25.6 17.1c-.5 0-1 .1-1.5.2-.2-3.9-3.4-7-7.3-7-.7 0-1.5.1-2.2.3-.3.1-.5.4-.5.7v11.2c0 .*******.7h10.8c2.1 0 3.9-1.7 3.9-3.9s-1.7-3.9-3.9-3.9zm-13.2-2.6c-.4 0-.7.3-.7.7v8.7c0 .*******.7s.7-.3.7-.7v-8.7c0-.4-.3-.7-.7-.7zm-2.7 1.5c-.4 0-.7.3-.7.7v7.2c0 .*******.7s.7-.3.7-.7v-7.2c0-.4-.3-.7-.7-.7zm-2.7 2.1c-.4 0-.7.3-.7.7v5.1c0 .*******.7s.7-.3.7-.7v-5.1c0-.4-.3-.7-.7-.7zm-2.7 2.3c-.4 0-.7.3-.7.7v2.8c0 .*******.7s.7-.3.7-.7v-2.8c0-.4-.3-.7-.7-.7z"/></svg>
    <span className="ml-2 font-medium">SoundCloud</span>
  </div>
);
const BandcampIcon = () => (
  <div className="flex items-center">
    <svg width="20" height="20" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true"><path d="M2 27.5h8.8L30 4.5H21.2z"/></svg>
    <span className="ml-2 font-medium">Bandcamp</span>
  </div>
);
const SpotifyIcon = () => (
  <div className="flex items-center">
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
      <path fillRule="evenodd" d="M19.0983701,10.6382791 C15.230178,8.34118115 8.85003755,8.12986439 5.15729493,9.25058527 C4.56433588,9.43062856 3.93727638,9.09580812 3.75758647,8.50284907 C3.57789655,7.90953664 3.91236362,7.28283051 4.50585273,7.10261054 C8.74455585,5.81598127 15.7909802,6.06440214 20.2440037,8.70780512 C20.7774195,9.02442687 20.9525156,9.71332656 20.6362472,10.2456822 C20.3198021,10.779098 19.6305491,10.9549008 19.0983701,10.6382791 M18.971686,14.0407262 C18.7004726,14.4810283 18.1246521,14.6190203 17.6848801,14.3486903 C14.4600027,12.3664473 9.54264764,11.792217 5.72728477,12.9503953 C5.23256328,13.0998719 4.70992535,12.8208843 4.55974204,12.3270462 C4.41061884,11.8323247 4.68978312,11.3107469 5.18362118,11.1602103 C9.5419409,9.83771368 14.9600247,10.4782013 18.6638986,12.7544503 C19.1036707,13.0253103 19.242016,13.6013075 18.971686,14.0407262 M17.5034233,17.308185 C17.2876894,17.6617342 16.827245,17.7725165 16.4749326,17.5571359 C13.6571403,15.8347984 10.1101639,15.4459119 5.93312425,16.4000177 C5.53063298,16.4922479 5.12937851,16.2399399 5.03767834,15.8376253 C4.94544812,15.4351341 5.19669597,15.0338796 5.60024736,14.9420027 C10.1712973,13.8970803 14.0923186,14.3467468 17.2551791,16.2796943 C17.6078449,16.4948982 17.7189805,16.9556959 17.5034233,17.308185 M12,0 C5.37267547,0 0,5.37249879 0,11.9998233 C0,18.6278546 5.37267547,24 12,24 C18.6275012,24 24,18.6278546 24,11.9998233 C24,5.37249879 18.6275012,0 12,0" />
    </svg>
    <span className="ml-2 font-medium">Spotify</span>
  </div>
);
const AppleMusicIcon = () => (
  <div className="flex items-center">
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
      <path d="M23.994 6.124a9.23 9.23 0 0 0-.24-2.19c-.317-1.31-1.062-2.31-2.18-3.043a5.022 5.022 0 0 0-1.877-.726 10.496 10.496 0 0 0-1.564-.15c-.04-.003-.083-.01-.124-.013H5.986c-.152.01-.303.017-.455.026-.747.043-1.49.123-2.193.4-1.336.53-2.3 1.452-2.865 2.78-.192.448-.292.925-.363 1.408a10.61 10.61 0 0 0-.1 1.18c0 .032-.007.062-.01.093v12.223c.01.14.017.283.027.424.05.815.154 1.624.497 2.373.65 1.42 1.738 2.353 3.234 2.801.42.127.856.187 1.293.228.555.053 1.11.06 1.667.06h11.03a12.5 12.5 0 0 0 1.57-.1c.822-.106 1.596-.35 2.295-.81a5.046 5.046 0 0 0 1.88-2.207c.186-.42.293-.87.37-1.324.113-.675.138-1.358.137-2.04-.002-3.8 0-7.595-.003-11.393zm-6.423 3.99v5.712c0 .417-.058.827-.244 1.206-.29.59-.76.962-1.388 1.14-.35.1-.706.157-1.07.173-.95.045-1.773-.6-1.943-1.536a1.88 1.88 0 0 1 1.038-2.022c.323-.16.67-.25 1.018-.324.378-.082.758-.153 1.134-.24.274-.063.457-.23.51-.516a.904.904 0 0 0 .02-.193c0-1.815 0-3.63-.002-5.443a.725.725 0 0 0-.026-.185c-.04-.15-.15-.243-.304-.234-.16.01-.318.035-.475.066-.76.15-1.52.303-2.28.456l-2.325.47-1.374.278c-.016.003-.032.01-.048.013-.277.077-.377.203-.39.49-.002.042 0 .086 0 .13-.002 2.602 0 5.204-.003 7.805 0 .42-.047.836-.215 1.227-.278.64-.77 1.04-1.434 1.233-.35.1-.71.16-1.075.172-.96.036-1.755-.6-1.92-1.544-.14-.812.23-1.685 1.154-2.075.357-.15.73-.232 1.108-.31.287-.06.575-.116.86-.177.383-.083.583-.323.6-.714v-.15c0-2.96 0-5.922.002-8.882 0-.123.013-.25.042-.37.07-.285.273-.448.546-.518.255-.066.515-.112.774-.165.733-.15 1.466-.296 2.2-.444l2.27-.46c.67-.134 1.34-.27 2.01-.403.22-.043.442-.088.663-.106.31-.025.523.17.554.482.008.073.012.148.012.223.002 1.91.002 3.822 0 5.732z" />
    </svg>
    <span className="ml-2 font-medium">Apple Music</span>
  </div>
);
const YouTubeMusicIcon = () => (
  <div className="flex items-center">
    <svg width="20" height="20" viewBox="0 0 32 32" fill="currentColor" aria-hidden="true">
      <path d="M13.106 20.424v-8.847l7.663 4.424zM16 8.217c-4.298 0-7.783 3.484-7.783 7.783s3.484 7.783 7.783 7.783c4.298 0 7.783-3.484 7.783-7.783v0c-0.007-4.296-3.487-7.776-7.782-7.783h-0.001zM16 24.877c0 0 0 0-0 0-4.903 0-8.877-3.975-8.877-8.877s3.975-8.877 8.877-8.877c4.903 0 8.877 3.975 8.877 8.877 0 0 0 0 0 0v0c0 0 0 0.001 0 0.001 0 4.902-3.974 8.877-8.877 8.877-0 0-0.001 0-0.001 0h0zM16 1.004c0 0 0 0-0 0-8.282 0-14.996 6.714-14.996 14.996s6.714 14.996 14.996 14.996c8.282 0 14.996-6.714 14.996-14.996v0c-0.003-8.281-6.715-14.993-14.995-14.996h-0z" />
    </svg>
    <span className="ml-2 font-medium">YouTube Music</span>
  </div>
);
const AmazonMusicIcon = () => (
  <div className="flex items-center">
    <svg width="20" height="20" viewBox="0 0 291.319 291.319" fill="currentColor" aria-hidden="true">
      <path d="M252.089,239.901c-120.033,57.126-194.528,9.331-242.214-19.7c-2.95-1.83-7.966,0.428-3.614,5.426 c15.886,19.263,67.95,65.692,135.909,65.692c68.005,0,108.462-37.107,113.523-43.58 C260.719,241.321,257.169,237.78,252.089,239.901z M285.8,221.284c-3.223-4.197-19.6-4.98-29.906-3.714 c-10.324,1.229-25.818,7.538-24.471,11.325c0.692,1.42,2.103,0.783,9.195,0.146c7.11-0.71,27.029-3.223,31.18,2.203 c4.17,5.462-6.354,31.49-8.275,35.687c-1.857,4.197,0.71,5.28,4.197,2.485c3.441-2.795,9.668-10.032,13.847-20.274 C285.718,238.845,288.249,224.479,285.8,221.284z" />
      <path d="M221.71,149.219V53.557C221.71,37.125,205.815,0,148.689,0C91.572,0,61.184,35.696,61.184,67.85 l47.74,4.27c0,0,10.633-32.136,35.313-32.136s22.987,19.992,22.987,24.316v20.784C135.607,86.149,57.096,95.18,57.096,161.382 c0,71.191,89.863,74.177,119.332,28.167c1.138,1.866,2.431,3.696,4.051,5.408c10.843,11.398,25.308,24.981,25.308,24.981 l36.852-36.415C242.658,183.513,221.71,167.071,221.71,149.219z M112.511,152.578c0-30.579,32.764-36.779,54.722-37.507v26.319 C167.224,193.527,112.511,185.634,112.511,152.578z" />
    </svg>
    <span className="ml-2 font-medium">Amazon Music</span>
  </div>
);

export const ReleaseCard = ({ release }: ReleaseCardProps) => {
  const [selectedTrackId, setSelectedTrackId] = useState(release.tracks[0]?.youtubeVideoId);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Scroll animation
  const cardAnimation = useScrollAnimation(0.2);

  function handleFormatDownload(format: string) {
    setShowDownloadModal(false);
    // Clean up title for URL (replace spaces with +)
    const albumTitle = release.artist.replace(/ /g, '+') + '+-+' + release.title.replace(/ /g, '+');
    const fileName = `${albumTitle}+-+${format.replace(/ /g, '+')}.zip`;
    const url = `https://auspex-records-releases.s3.us-west-1.amazonaws.com/${albumTitle}/${fileName}`;
    window.open(url, '_blank');
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    },
    hover: {
      y: -10,
      scale: 1.02,
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  const coverVariants = {
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
        ease: "easeInOut"
      }
    },
    tap: {
      scale: 0.95,
      transition: {
        duration: 0.1
      }
    }
  };

  return (
    <motion.div
      ref={cardAnimation.ref}
      className="glass-effect rounded-xl overflow-hidden shadow-glow hover:shadow-glow-strong hover-lift"
      variants={scaleInVariants}
      initial="hidden"
      animate={cardAnimation.controls}
      whileHover="hover"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="p-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Cover Art */}
          <motion.div
            className="w-full md:w-64 flex-shrink-0"
            variants={coverVariants}
            whileHover="hover"
          >
            <motion.div
              className="relative overflow-hidden rounded-xl shadow-2xl"
              whileHover={{ boxShadow: "0 20px 40px rgba(255, 79, 0, 0.3)" }}
            >
              <img
                src={release.coverUrl}
                alt={`${release.title} by ${release.artist}`}
                className="w-full h-auto transition-all duration-500"
              />
              <motion.div
                className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0"
                animate={{ opacity: isHovered ? 1 : 0 }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>

            {/* Platform Links */}
            <motion.div
              className="mt-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <motion.button
                type="button"
                className="bg-gradient-primary text-white px-6 py-3 rounded-full text-sm font-semibold w-full flex items-center justify-center gap-2 mb-4 shadow-glow font-body"
                onClick={() => setShowDownloadModal(true)}
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                <HiDownload className="text-lg" />
                Download
              </motion.button>
              <AnimatePresence>
                {showDownloadModal && (
                  <motion.div
                    className="fixed inset-0 flex items-center justify-center z-50 bg-black/80 backdrop-blur-sm"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    onClick={() => setShowDownloadModal(false)}
                  >
                    <motion.div
                      className="backdrop-blur-glass rounded-xl shadow-glow p-6 max-w-sm w-full mx-4 relative border border-auspex-orange-500/20"
                      initial={{ scale: 0.8, opacity: 0, y: 50 }}
                      animate={{ scale: 1, opacity: 1, y: 0 }}
                      exit={{ scale: 0.8, opacity: 0, y: 50 }}
                      onClick={(e) => e.stopPropagation()}
                    >
                      <motion.button
                        className="absolute top-3 right-3 text-gray-400 hover:text-auspex-orange-500 text-xl p-1 rounded-full hover:bg-auspex-navy-800/50"
                        onClick={() => setShowDownloadModal(false)}
                        aria-label="Close"
                        whileHover={{ scale: 1.1, rotate: 90 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <HiX />
                      </motion.button>
                      <h3 className="text-xl font-bold text-gradient mb-6 text-center">Choose Audio Format</h3>
                      <motion.div
                        className="mb-6"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 }}
                      >
                        <h4 className="text-auspex-orange-400 font-semibold mb-3">Lossy</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {['MP3 320', 'MP3 V0', 'AAC', 'Ogg Vorbis'].map((fmt, index) => (
                            <motion.button
                              key={fmt}
                              className="glass-effect hover:bg-gradient-primary text-white px-3 py-2 rounded-lg text-sm w-full font-medium border border-auspex-orange-500/20"
                              onClick={() => handleFormatDownload(fmt)}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: 0.1 + index * 0.05 }}
                              whileHover={{ scale: 1.05, y: -2 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              {fmt}
                            </motion.button>
                          ))}
                        </div>
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                      >
                        <h4 className="text-auspex-orange-400 font-semibold mb-3">Lossless</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {['FLAC', 'WAV', 'AIFF', 'ALAC'].map((fmt, index) => (
                            <motion.button
                              key={fmt}
                              className="glass-effect hover:bg-gradient-primary text-white px-3 py-2 rounded-lg text-sm w-full font-medium border border-auspex-orange-500/20"
                              onClick={() => handleFormatDownload(fmt)}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: 0.2 + index * 0.05 }}
                              whileHover={{ scale: 1.05, y: -2 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              {fmt}
                            </motion.button>
                          ))}
                        </div>
                      </motion.div>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Streaming platforms heading */}
              <h4 className="text-sm font-semibold text-gray-400 mb-3 border-b border-gray-800 pb-2">
                Streaming platforms
              </h4>

              <ul className="flex flex-col gap-2">
                {release.platforms.bandcamp && (
                  <li>
                    <a
                      href={release.platforms.bandcamp}
                      className="flex items-center gap-2 bg-[#1DA0C3] text-white px-4 py-2 rounded-full text-sm hover:opacity-90 transition-opacity w-full"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <BandcampIcon />
                    </a>
                  </li>
                )}
                {release.platforms.soundcloud && (
                  <li>
                    <a
                      href={release.platforms.soundcloud}
                      className="flex items-center gap-2 bg-[#FF5500] text-white px-4 py-2 rounded-full text-sm hover:opacity-90 transition-opacity w-full"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <SoundCloudIcon />
                    </a>
                  </li>
                )}
                {release.platforms.spotify && (
                  <li>
                    <a
                      href={release.platforms.spotify}
                      className="flex items-center gap-2 bg-[#1DB954] text-white px-4 py-2 rounded-full text-sm hover:opacity-90 transition-opacity w-full"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <SpotifyIcon />
                    </a>
                  </li>
                )}
                {release.platforms.appleMusicUrl && (
                  <li>
                    <a
                      href={release.platforms.appleMusicUrl}
                      className="flex items-center gap-2 bg-[#FB233B] text-white px-4 py-2 rounded-full text-sm hover:opacity-90 transition-opacity w-full"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AppleMusicIcon />
                    </a>
                  </li>
                )}
                {release.platforms.youtubeMusicUrl && (
                  <li>
                    <a
                      href={release.platforms.youtubeMusicUrl}
                      className="flex items-center gap-2 bg-[#FF0000] text-white px-4 py-2 rounded-full text-sm hover:opacity-90 transition-opacity w-full"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <YouTubeMusicIcon />
                    </a>
                  </li>
                )}
                {release.platforms.amazonMusicUrl && (
                  <li>
                    <a
                      href={release.platforms.amazonMusicUrl}
                      className="flex items-center gap-2 bg-[#00A8E1] text-white px-4 py-2 rounded-full text-sm hover:opacity-90 transition-opacity w-full"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AmazonMusicIcon />
                    </a>
                  </li>
                )}
              </ul>
            </motion.div>
          </motion.div>

          {/* Release Info and Tracks */}
          <motion.div
            className="flex-1"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <motion.h2
              className="text-3xl font-heading font-bold text-gradient mb-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              {release.title}
            </motion.h2>
            <motion.h3
              className="text-xl text-gray-300 mb-6 font-body"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              {release.artist}
            </motion.h3>

            {/* Track List */}
            <motion.div
              className="mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <h4 className="text-lg font-heading font-semibold text-auspex-orange-400 mb-4">Tracks</h4>
              <div className="space-y-2">
                {release.tracks.map((track, index) => (
                  <motion.button
                    key={track.id}
                    onClick={() => setSelectedTrackId(track.youtubeVideoId)}
                    className={`w-full text-left px-4 py-3 rounded-lg transition-all duration-300 font-body ${
                      selectedTrackId === track.youtubeVideoId
                        ? "bg-gradient-primary text-white shadow-glow"
                        : "glass-effect text-gray-300 hover:bg-auspex-navy-500/20 hover:text-white border border-auspex-navy-400/20"
                    }`}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    whileHover={{ scale: 1.02, x: 5 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center gap-2">
                      {selectedTrackId === track.youtubeVideoId && (
                        <HiPlay className="text-sm" />
                      )}
                      {track.title}
                    </div>
                  </motion.button>
                ))}
              </div>
            </motion.div>

            {/* YouTube Embed */}
            <AnimatePresence mode="wait">
              {selectedTrackId && (
                <motion.div
                  className="mt-6"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30 }}
                  transition={{ duration: 0.5 }}
                  key={selectedTrackId}
                >
                  <YouTubeEmbed
                    videoId={selectedTrackId}
                    title={
                      release.tracks.find((t) => t.youtubeVideoId === selectedTrackId)?.title ||
                      ""
                    }
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};
