{"name": "auspex-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:staging": "tsc -b && VITE_API_URL=https://ma1l0omu6k.execute-api.us-west-1.amazonaws.com vite build", "build:prod": "tsc -b && VITE_API_URL=https://e4n0a6vbud.execute-api.us-west-1.amazonaws.com vite build", "deploy:staging": "npm run build:staging && cd terraform/environments/staging && aws s3 sync ../../../dist/ s3://$(terraform output -raw s3_bucket_name)", "deploy:prod": "npm run build:prod && cd terraform/environments/prod && aws s3 sync ../../../dist/ s3://$(terraform output -raw s3_bucket_name)", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "preview": "vite preview", "prepare": "husky install"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^7.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^24.2.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "husky": "^9.1.7", "lint-staged": "^16.1.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}